{% load static %}

<!-- Featured Recipes Auto-Sliding Carousel -->
<div class="featured-carousel-container" id="featured-carousel-container">
    <div class="featured-carousel-wrapper">        
        <div class="featured-carousel" id="featured-carousel">
            <div class="carousel-track" id="carousel-track">
                <!-- Carousel items will be loaded here via JavaScript -->
                <div class="carousel-item loading-placeholder">
                    <div class="carousel-card">
                        <div class="carousel-image-placeholder">
                            <div class="loading-spinner"></div>
                        </div>
                        <div class="carousel-content">
                            <h4>Loading...</h4>
                            <p>Fetching delicious recipes</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Navigation dots -->
            <div class="carousel-dots" id="carousel-dots">
                <!-- Dots will be generated by JavaScript -->
            </div>
            
            <!-- Navigation arrows -->
            <button class="carousel-nav carousel-prev" id="carousel-prev" aria-label="Previous recipe">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="15,18 9,12 15,6"></polyline>
                </svg>
            </button>
            <button class="carousel-nav carousel-next" id="carousel-next" aria-label="Next recipe">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="9,18 15,12 9,6"></polyline>
                </svg>
            </button>
        </div>
        
        <!-- Call to action -->
        <div class="carousel-cta">
            <a href="{% url 'recipes:discover' %}" class="carousel-cta-button">
                <span>Explore More Recipes</span>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="7" y1="17" x2="17" y2="7"></line>
                    <polyline points="7,7 17,7 17,17"></polyline>
                </svg>
            </a>
        </div>
    </div>
</div>

<script>
// Featured Carousel Controller
class FeaturedCarousel {
    constructor() {
        this.container = document.getElementById('featured-carousel');
        this.track = document.getElementById('carousel-track');
        this.dotsContainer = document.getElementById('carousel-dots');
        this.prevBtn = document.getElementById('carousel-prev');
        this.nextBtn = document.getElementById('carousel-next');
        
        this.currentIndex = 0;
        this.recipes = [];
        this.autoSlideInterval = null;
        this.autoSlideDelay = 4000; // 4 seconds
        
        this.init();
    }
    
    async init() {
        try {
            await this.loadRecipes();
            this.setupEventListeners();
            this.startAutoSlide();
        } catch (error) {
            console.error('Failed to initialize featured carousel:', error);
            this.showError();
        }
    }
    
    async loadRecipes() {
        try {
            // Load 6 random recipes from TheMealDB
            const recipes = [];
            for (let i = 0; i < 6; i++) {
                const response = await fetch('https://www.themealdb.com/api/json/v1/1/random.php');
                const data = await response.json();
                if (data.meals && data.meals[0]) {
                    recipes.push(data.meals[0]);
                }
            }
            
            this.recipes = recipes;
            this.renderCarousel();
            this.generateDots();
        } catch (error) {
            console.error('Error loading recipes:', error);
            throw error;
        }
    }
    
    renderCarousel() {
        if (this.recipes.length === 0) return;
        
        this.track.innerHTML = this.recipes.map((recipe, index) => `
            <div class="carousel-item ${index === 0 ? 'active' : ''}" data-index="${index}">
                <div class="carousel-card" onclick="window.open('${this.getRecipeUrl(recipe)}', '_blank')">
                    <div class="carousel-image">
                        <img src="${recipe.strMealThumb}" alt="${recipe.strMeal}" loading="lazy">
                        <div class="carousel-overlay">
                            <span class="carousel-category">${recipe.strCategory || 'Recipe'}</span>
                        </div>
                    </div>
                    <div class="carousel-content">
                        <h4 class="carousel-recipe-title">${recipe.strMeal}</h4>
                        <p class="carousel-recipe-area">${recipe.strArea || 'International'} Cuisine</p>
                        <div class="carousel-recipe-meta">
                            <span class="carousel-meta-item">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                30 min
                            </span>
                            <span class="carousel-meta-item">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
                                </svg>
                                4.5
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    generateDots() {
        this.dotsContainer.innerHTML = this.recipes.map((_, index) => `
            <button class="carousel-dot ${index === 0 ? 'active' : ''}" 
                    data-index="${index}" 
                    aria-label="Go to recipe ${index + 1}"></button>
        `).join('');
    }
    
    setupEventListeners() {
        // Navigation buttons
        this.prevBtn?.addEventListener('click', () => this.goToPrevious());
        this.nextBtn?.addEventListener('click', () => this.goToNext());
        
        // Dots navigation
        this.dotsContainer?.addEventListener('click', (e) => {
            if (e.target.classList.contains('carousel-dot')) {
                const index = parseInt(e.target.dataset.index);
                this.goToSlide(index);
            }
        });
        
        // Pause auto-slide on hover
        this.container?.addEventListener('mouseenter', () => this.pauseAutoSlide());
        this.container?.addEventListener('mouseleave', () => this.startAutoSlide());
        
        // Touch/swipe support for mobile
        this.setupTouchEvents();
    }
    
    setupTouchEvents() {
        let startX = 0;
        let endX = 0;
        
        this.track?.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        });
        
        this.track?.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            const diff = startX - endX;
            
            if (Math.abs(diff) > 50) { // Minimum swipe distance
                if (diff > 0) {
                    this.goToNext();
                } else {
                    this.goToPrevious();
                }
            }
        });
    }
    
    goToSlide(index) {
        if (index < 0 || index >= this.recipes.length) return;
        
        this.currentIndex = index;
        this.updateCarousel();
        this.restartAutoSlide();
    }
    
    goToNext() {
        this.currentIndex = (this.currentIndex + 1) % this.recipes.length;
        this.updateCarousel();
        this.restartAutoSlide();
    }
    
    goToPrevious() {
        this.currentIndex = this.currentIndex === 0 ? this.recipes.length - 1 : this.currentIndex - 1;
        this.updateCarousel();
        this.restartAutoSlide();
    }
    
    updateCarousel() {
        // Update active item
        const items = this.track?.querySelectorAll('.carousel-item');
        items?.forEach((item, index) => {
            item.classList.toggle('active', index === this.currentIndex);
        });
        
        // Update active dot
        const dots = this.dotsContainer?.querySelectorAll('.carousel-dot');
        dots?.forEach((dot, index) => {
            dot.classList.toggle('active', index === this.currentIndex);
        });
        
        // Transform track
        if (this.track) {
            this.track.style.transform = `translateX(-${this.currentIndex * 100}%)`;
        }
    }
    
    startAutoSlide() {
        this.pauseAutoSlide();
        this.autoSlideInterval = setInterval(() => {
            this.goToNext();
        }, this.autoSlideDelay);
    }
    
    pauseAutoSlide() {
        if (this.autoSlideInterval) {
            clearInterval(this.autoSlideInterval);
            this.autoSlideInterval = null;
        }
    }
    
    restartAutoSlide() {
        this.startAutoSlide();
    }
    
    getRecipeUrl(recipe) {
        // For now, link to TheMealDB recipe page
        return `https://www.themealdb.com/meal/${recipe.idMeal}`;
    }
    
    showError() {
        this.track.innerHTML = `
            <div class="carousel-item active">
                <div class="carousel-card error">
                    <div class="carousel-content">
                        <h4>Unable to load recipes</h4>
                        <p>Please check your connection and try again</p>
                    </div>
                </div>
            </div>
        `;
    }
}

// Initialize carousel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('featured-carousel-container')) {
        new FeaturedCarousel();
    }
});
</script>
