{% load static %}

<!-- Featured Recipes Continuous Carousel -->
<div class="featured-carousel-container" id="featured-carousel-container">
    <div class="progress-bar">
        <div class="progress-fill"></div>
    </div>

    <div class="carousel-wrapper">
        <div class="carousel-track" id="carousel-track">
            <!-- Loading placeholder -->
            <div class="carousel-slide">
                <div class="slide-item loading-item">
                    <div class="slide-overlay">
                        <h3 class="slide-title">Loading...</h3>
                        <p class="slide-description">Fetching delicious recipes</p>
                    </div>
                </div>
                <div class="slide-item loading-item">
                    <div class="slide-overlay">
                        <h3 class="slide-title">Loading...</h3>
                        <p class="slide-description">Fetching delicious recipes</p>
                    </div>
                </div>
                <div class="slide-item loading-item">
                    <div class="slide-overlay">
                        <h3 class="slide-title">Loading...</h3>
                        <p class="slide-description">Fetching delicious recipes</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Call to action overlay -->
    <div class="carousel-cta-overlay">
        <a href="{% url 'recipes:discover' %}" class="carousel-cta-button">
            <span>Explore More</span>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="7" y1="17" x2="17" y2="7"></line>
                <polyline points="7,7 17,7 17,17"></polyline>
            </svg>
        </a>
    </div>
</div>

<script>
// Featured Carousel Controller - Continuous Sliding
class FeaturedCarousel {
    constructor() {
        this.container = document.getElementById('featured-carousel-container');
        this.track = document.getElementById('carousel-track');
        this.recipes = [];
        this.init();
    }

    async init() {
        try {
            await this.loadRecipes();
            this.setupEventListeners();
        } catch (error) {
            console.error('Failed to initialize featured carousel:', error);
            this.showError();
        }
    }
    
    async loadRecipes() {
        try {
            // Load 15 random recipes from TheMealDB for continuous sliding
            const recipes = [];
            for (let i = 0; i < 15; i++) {
                const response = await fetch('https://www.themealdb.com/api/json/v1/1/random.php');
                const data = await response.json();
                if (data.meals && data.meals[0]) {
                    recipes.push(data.meals[0]);
                }
            }

            this.recipes = recipes;
            this.renderCarousel();
        } catch (error) {
            console.error('Error loading recipes:', error);
            throw error;
        }
    }
    
    renderCarousel() {
        if (this.recipes.length === 0) return;

        // Create 5 slides with 3 recipes each
        const slides = [];
        for (let i = 0; i < 5; i++) {
            const slideRecipes = this.recipes.slice(i * 3, (i + 1) * 3);
            slides.push(slideRecipes);
        }

        this.track.innerHTML = slides.map(slideRecipes => `
            <div class="carousel-slide">
                ${slideRecipes.map(recipe => `
                    <div class="slide-item"
                         style="background-image: url('${recipe.strMealThumb}');"
                         onclick="window.open('${this.getRecipeUrl(recipe)}', '_blank')">
                        <div class="slide-overlay">
                            <h3 class="slide-title">${recipe.strMeal}</h3>
                            <p class="slide-description">${recipe.strArea || 'International'} • ${recipe.strCategory || 'Recipe'}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
        `).join('');
    }
    
    setupEventListeners() {
        // Pause animation on hover
        this.container?.addEventListener('mouseenter', () => {
            this.track.style.animationPlayState = 'paused';
        });

        this.container?.addEventListener('mouseleave', () => {
            this.track.style.animationPlayState = 'running';
        });
    }
    

    
    getRecipeUrl(recipe) {
        // For now, link to TheMealDB recipe page
        return `https://www.themealdb.com/meal/${recipe.idMeal}`;
    }

    showError() {
        this.track.innerHTML = `
            <div class="carousel-slide">
                <div class="slide-item loading-item">
                    <div class="slide-overlay">
                        <h3 class="slide-title">Unable to load recipes</h3>
                        <p class="slide-description">Please check your connection and try again</p>
                    </div>
                </div>
            </div>
        `;
    }
}

// Initialize carousel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('featured-carousel-container')) {
        new FeaturedCarousel();
    }
});
</script>
